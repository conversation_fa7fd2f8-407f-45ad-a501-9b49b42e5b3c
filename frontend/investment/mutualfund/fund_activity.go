package mutualfund

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	errorspb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	types "github.com/epifi/gamma/api/typesv2"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	"github.com/epifi/gamma/pkg/investment"
)

// TODO(Ayush): Remove this after m74 is pushed to prod
var stateDisplayMap = map[string]string{
	"ACTIVITY_STATE_CREATED":             "Processing",
	"ACTIVITY_STATE_PUSHED_TO_VENDOR":    "Processing",
	"ACTIVITY_STATE_SUCCESS":             "Success",
	"ACTIVITY_STATE_FAILED":              "Failed",
	"ACTIVITY_STATE_MANUAL_INTERVENTION": ProcessingDelayed,
}
var sourceDisplayMap = map[string]string{
	"ACTIVITY_TYPE_ONE_TIME_INVEST_BUY":      "One-time investment",
	"ACTIVITY_TYPE_AUTO_INVEST_WITH_FIT_BUY": "AutoInvest with FIT",
	"ACTIVITY_TYPE_WITHDRAWAL":               "Withdrawal",
}

const (
	FUND_ACTIVITY_DEFUALT_ERROR_CODE        = "FA0001"
	FUND_ACTIVITY_DEFAULT_ERROR_TITLE       = "Uh-oh! This should not have happened."
	FUND_ACTIVITY_DEFAULT_ERROR_DESCRIPTION = "Looks like the fund activity failed to load. Please try again."
	FUND_ACTIVITY_ETA_SUB_TEXT              = "ETA %s"
)

func (s *Service) GetMutualFundActivity(ctx context.Context, feReq *fePb.GetMutualFundActivityRequest) (*fePb.GetMutualFundActivityResponse, error) {

	versionCode := feReq.GetReq().GetAppVersionCode()
	platform := feReq.GetReq().GetAuth().GetDevice().GetPlatform()

	ordersResponse, err := s.orderManagerClient.GetOrdersByFundIDAndActorID(ctx, &orderPb.GetOrdersByFundIDAndActorIDRequest{
		ActorId:      feReq.Req.Auth.ActorId,
		MutualFundId: feReq.MutualFundId,
		PageContext:  feReq.PageContext,
	})

	if err != nil {
		logger.Error(ctx, "error in invoking GetOrdersByFundIDAndActorID",
			zap.String(logger.ACTOR_ID, feReq.Req.Auth.ActorId),
			zap.String(logger.MF_ID, feReq.MutualFundId),
			zap.Error(err))
		return s.getMutualFundActivityErrorResponse("error while fetching fund activity"), nil
	}

	if !ordersResponse.Status.IsSuccess() {
		logger.Error(ctx,
			fmt.Sprintf("non-success response on invoking GetOrdersByFundIDAndActorID with shortMessage:%s and DebugMessage: %s",
				ordersResponse.Status.ShortMessage, ordersResponse.Status.DebugMessage),
			zap.String(logger.ACTOR_ID, feReq.Req.Auth.ActorId),
			zap.String(logger.MF_ID, feReq.MutualFundId))

		return s.getMutualFundActivityErrorResponse("fetching fund activity failed"), nil
	}

	fundActivities, err := s.convertOrdersToFundActivities(ctx, ordersResponse.Orders, ordersResponse.MutualFund, versionCode, platform)
	if err != nil {
		logger.Error(ctx, "error in converting orders to fundActivities",
			zap.String(logger.ACTOR_ID, feReq.Req.Auth.ActorId),
			zap.String(logger.MF_ID, feReq.MutualFundId),
			zap.Error(err))

		return s.getMutualFundActivityErrorResponse("error in converting orders to fundActivities"), nil
	}
	fundActivityResponse := &fePb.GetMutualFundActivityResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageContext:             ordersResponse.PageContext,
		FundActivities:          fundActivities,
		TotalOrders:             ordersResponse.TotalOrders,
		StateDisplayData:        stateDisplayMap,
		ActivityTypeDisplayData: sourceDisplayMap,
	}

	return fundActivityResponse, nil
}

func (s *Service) convertOrdersToFundActivities(ctx context.Context, orders []*orderPb.Order, mf *mfPb.MutualFund, versionCode uint32, platform commontypes.Platform) ([]*fePb.FundActivity, error) {
	var fundActivities []*fePb.FundActivity

	for _, order := range orders {
		activityState := s.convertOrderStatusToActivityState(order, versionCode, platform)
		fundActivity := &fePb.FundActivity{
			Amount:                  types.GetFromBeMoney(order.Amount),
			ActivityType:            s.convertOrderTypeToActivityType(order),
			ActivityState:           activityState,
			Units:                   order.Units,
			CreatedAt:               order.CreatedAt,
			UpdatedAt:               order.UpdatedAt,
			OrderId:                 order.Id,
			RtaConfirmedAmount:      types.GetFromBeMoney(order.GetRtaConfirmedAmount()),
			ActivityStateDisplayStr: s.convertActivityStateToDisplayString(order, activityState),
		}

		// Add ETA if order is not in Success or Failed state
		if activityState != fePb.ActivityState_ACTIVITY_STATE_SUCCESS && activityState != fePb.ActivityState_ACTIVITY_STATE_FAILED {
			paymentRes, paymentErr := s.phClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
				OrderId:     order.GetId(),
				PaymentMode: phPb.PaymentMode(order.GetPaymentMode()),
			})
			if err := epifigrpc.RPCError(paymentRes, paymentErr); err != nil {
				if !rpc.StatusFromError(err).IsRecordNotFound() {
					return nil, err
				}
			}

			etaDate, err := investment.GetETADate(investment.ETAParams{
				PendingOrder:  order,
				AssetClass:    mf.AssetClass,
				PaymentStatus: paymentRes.GetPaymentStatus(),
				PaymentTime:   paymentRes.GetTransactionTime(),
				CategoryName:  mf.CategoryName,
			})
			if err != nil {
				return nil, err
			}
			var etaText string
			if etaDate.Before(time.Now()) {
				fundActivity.ActivityStateDisplayStr = ProcessingDelayed
			} else {
				etaText = fmt.Sprintf(FUND_ACTIVITY_ETA_SUB_TEXT, etaDate.Format("02 January"))
				fundActivity.Eta = etaText
			}
		}

		fundActivities = append(fundActivities, fundActivity)
	}
	return fundActivities, nil
}

func (s *Service) convertOrderTypeToActivityType(order *orderPb.Order) fePb.ActivityType {

	if order.OrderType == orderPb.OrderType_SELL {
		return fePb.ActivityType_ACTIVITY_TYPE_WITHDRAWAL
	}

	if order.Client == orderPb.OrderClient_FIT {
		return fePb.ActivityType_ACTIVITY_TYPE_AUTO_INVEST_WITH_FIT_BUY
	}

	return fePb.ActivityType_ACTIVITY_TYPE_ONE_TIME_INVEST_BUY
}

func (s *Service) convertOrderStatusToActivityState(order *orderPb.Order, versionCode uint32, platform commontypes.Platform) fePb.ActivityState {

	switch order.OrderStatus {
	case orderPb.OrderStatus_CREATED, orderPb.OrderStatus_PRE_PROCESSING, orderPb.OrderStatus_PRE_PROCESSING_COMPLETE,
		orderPb.OrderStatus_INITIATED, orderPb.OrderStatus_SENT_TO_RTA:
		return fePb.ActivityState_ACTIVITY_STATE_CREATED
	case orderPb.OrderStatus_ACCEPTED_BY_RTA, orderPb.OrderStatus_IN_PAYMENT,
		orderPb.OrderStatus_PAID, orderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT, orderPb.OrderStatus_IN_FULFILLMENT, orderPb.OrderStatus_IN_SETTLEMENT:
		return fePb.ActivityState_ACTIVITY_STATE_PUSHED_TO_VENDOR
	case orderPb.OrderStatus_SETTLED, orderPb.OrderStatus_CONFIRMED_BY_RTA:
		return fePb.ActivityState_ACTIVITY_STATE_SUCCESS
	case orderPb.OrderStatus_FAILURE, orderPb.OrderStatus_EXPIRED:
		return fePb.ActivityState_ACTIVITY_STATE_FAILED
	case orderPb.OrderStatus_MANUAL_INTERVENTION:
		return s.getActivityStateForManualIntervention(versionCode, platform)
	default:
		return fePb.ActivityState_ACTIVITY_STATE_CREATED
	}
}

func (s *Service) getActivityStateForManualIntervention(versionCode uint32, platform commontypes.Platform) fePb.ActivityState {

	var minAppVersion int
	if platform == commontypes.Platform_ANDROID {
		minAppVersion = s.config.Investment().FundActivityManualInterventionSupport().MinAndroidVersion
	} else {
		minAppVersion = s.config.Investment().FundActivityManualInterventionSupport().MinIosVersion
	}

	// The previous app versions don't support the proto ACTIVITY_STATE_MANUAL_INTERVENTION.
	if int(versionCode) < minAppVersion {
		return fePb.ActivityState_ACTIVITY_STATE_CREATED
	} else {
		return fePb.ActivityState_ACTIVITY_STATE_MANUAL_INTERVENTION
	}

}

func (s *Service) convertActivityStateToDisplayString(order *orderPb.Order, state fePb.ActivityState) string {

	// ToDo(Junaid): Remove this once next force upgrade of ios and android is done
	if order.OrderStatus == orderPb.OrderStatus_MANUAL_INTERVENTION {
		return ProcessingDelayed
	}

	switch state {
	case fePb.ActivityState_ACTIVITY_STATE_CREATED, fePb.ActivityState_ACTIVITY_STATE_PUSHED_TO_VENDOR:
		return Processing
	case fePb.ActivityState_ACTIVITY_STATE_SUCCESS:
		return Success
	case fePb.ActivityState_ACTIVITY_STATE_FAILED:
		return Failed
	case fePb.ActivityState_ACTIVITY_STATE_MANUAL_INTERVENTION:
		return ProcessingDelayed
	default:
		return Processing
	}
}

func (s *Service) getMutualFundActivityErrorResponse(debugMessage string) *fePb.GetMutualFundActivityResponse {
	retryCta := &errorspb.CTA{
		Type: errorspb.CTA_RETRY,
		Text: "RETRY",
	}
	errorView := feErrors.NewBottomSheetCardErrorView(FUND_ACTIVITY_DEFUALT_ERROR_CODE, FUND_ACTIVITY_DEFAULT_ERROR_TITLE, "",
		FUND_ACTIVITY_DEFAULT_ERROR_DESCRIPTION, retryCta)
	return &fePb.GetMutualFundActivityResponse{
		RespHeader: &header.ResponseHeader{
			Status:    rpc.StatusInternalWithDebugMsg(debugMessage),
			ErrorView: errorView,
		},
	}
}
