// nolint
package order

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	etaHandlerPb "github.com/epifi/gamma/api/investment/mutualfund/eta_handler"
	"github.com/epifi/gamma/api/investment/mutualfund/notifications"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/investment/mutualfund/metrics"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

var (
	ErrOrderAmtExceedRedeemableAmount = fmt.Errorf("unitsToSell is greater than the totalRedeemableUnits")
)

// CreateOrder receives the orders from the clients and records in the database.
// Orders can be sent by various clients like FIT, Users directly via app.
//
// This implementation will be idempotent i.e. if the same order is received again from the same client, it should not
// be processed again and an error will be returned. De-duplication can be done on the basis of client_order_id in the request.
func (s *Service) CreateOrder(ctx context.Context, req *pb.CreateOrderRequest) (*pb.CreateOrderResponse, error) {
	// TODO(anand): [Ticket 18012] KIBANA dashboard setup. Remove later when analytics is setup.
	logger.Info(ctx, "received new mf order", zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId),
		zap.String(logger.ACTOR_ID, req.ActorId), zap.String(logger.MF_ID, req.MutualFundId), zap.String(logger.ORDER_TYPE,
			req.OrderType.String()), zap.String(logger.CLIENT, req.Client.String()))
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "securely logging new mf order", zap.String(logger.ACTOR_ID, req.ActorId),
		zap.String(logger.MF_ID, req.MutualFundId), zap.String(logger.ORDER_TYPE, req.OrderType.String()),
		zap.String(logger.ORDER_SUBTYPE, req.OrderSubType.String()), zap.String(logger.AMOUNT, req.Amount.String()), zap.String(logger.CLIENT, req.Client.String()))

	/*
		check whether the order is eligible to be placed or not.
	*/

	if s.cfg.CheckOrderEligibility().EnableCheckOrderEligibility() {
		isActorEligible, dl, ineligibilityReason, eligibilityErr := s.checkOrderEligibility(ctx, req)
		if eligibilityErr != nil {
			logger.Error(ctx, "checkOrderEligibility error", zap.String(logger.ACTOR_ID, req.ActorId), zap.Error(eligibilityErr))
			return s.getCreateOrderErrorResponse("checkOrderEligibility error", eligibilityErr), nil
		}
		if !isActorEligible {
			createOrderRes := &pb.CreateOrderResponse{
				Status:             &rpc.Status{ShortMessage: fmt.Sprintf("Actor not eligible due to %s", ineligibilityReason.String())},
				NextOnboardingStep: dl,
			}
			switch ineligibilityReason {
			case pb.IneligibilityReason_DOB_MISMATCH:
				createOrderRes.GetStatus().Code = uint32(pb.CreateOrderResponse_ACTOR_INELIGIBLE_DOB_MISMATCH)
				return createOrderRes, nil
			case pb.IneligibilityReason_AADHAAR_POA_NOT_VALIDATED:
				createOrderRes.GetStatus().Code = uint32(pb.CreateOrderResponse_ACTOR_INELIGIBLE_AADHAAR_POA_NOT_VALIDATED)
				return createOrderRes, nil
			default:
				logger.Error(ctx, "unhandled ineligibility reason", zap.String(logger.REASON, ineligibilityReason.String()))
				createOrderRes.GetStatus().Code = uint32(pb.CreateOrderResponse_ACTOR_INELIGIBLE)
				return createOrderRes, nil
			}
		}
	}
	// TODO(Ayush) : Change this to just fetch AMC info
	fundInfo, err := s.fetchMutualFundInfo(ctx, req.MutualFundId)
	if err != nil {
		logger.Error(ctx, "error while fetching mutual fund info", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(err))

		return s.getCreateOrderErrorResponse("error while fetching mutual fund info", err), nil
	}

	amcInfo, err := s.amcInfoDao.GetByAmc(ctx, fundInfo.Amc)
	if err != nil {
		logger.Error(ctx, "error while fetching amc info", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(err),
			zap.String(logger.MF_AMC, fundInfo.Amc.String()))
		return s.getCreateOrderErrorResponse("error while fetching mutual fund info", err), nil
	}

	switch req.OrderType {
	case pb.OrderType_BUY:
		return s.processBuyOrderV2(ctx, req, fundInfo, amcInfo)

	case pb.OrderType_SELL:
		return s.processSellOrderV2(ctx, req, fundInfo, amcInfo)

	default:
		return s.getCreateOrderErrorResponse(fmt.Sprintf("unexpected orderType: %s", req.OrderType),
			fmt.Errorf("orderType: %s is not supported", req.OrderType)), nil
	}
}

func (s *Service) processBuyOrderV2(ctx context.Context, req *pb.CreateOrderRequest, fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo) (*pb.CreateOrderResponse, error) {
	orders, createErr, status := s.buyOrderProcessor.CreateOrder(ctx, req, fundInfo, amcInfo)
	if createErr != nil {
		logger.Error(ctx, "error in CreateOrder", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(createErr),
			zap.String(logger.MF_AMC, fundInfo.Amc.String()))

		return s.getCreateOrderErrorResponse("error in createOrder for buy", createErr, uint32(status)), nil
	}
	if len(orders) == 0 {
		logger.Error(ctx, "error in CreateOrder, no order created", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()),
			zap.String(logger.STATUS, status.String()),
			zap.String(logger.MF_AMC, fundInfo.Amc.String()))
		return s.getCreateOrderErrorResponse("error in createOrder for buy", fmt.Errorf("error in CreateOrder, no order created"), uint32(pb.CreateOrderResponse_INTERNAL)), nil
	}
	go metrics.RecordOrderCreation(orders[0].OrderStatus)
	// TODO(anand):  [Ticket 18012] KIBANA dashboard setup. Remove later when analytics is setup.
	logger.Info(ctx, fmt.Sprintf("mf order created with type %s and subtype %s", orders[0].OrderType.String(),
		orders[0].OrderSubType.String()), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId),
		zap.String(logger.ACTOR_ID, req.ActorId), zap.String(logger.MF_ORDER, orders[0].Id))

	clonedCtxWithIncreasedDeadline, cancelCtx := context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(5*time.Minute))
	enableSendOrderDelayedPN := s.cfg.SendOrderDelayedPNconfig().EnableSendOrderDelayedPNconfig()
	go func() {
		defer cancelCtx()
		if enableSendOrderDelayedPN {
			_, publishErr := s.orderDelayedNotificationPublisher.PublishWithDelay(clonedCtxWithIncreasedDeadline, &notifications.SendDelayedOrderNotificationRequest{OrderId: orders[0].GetId()}, invPkg.DurationUntilNextEtaCheck)
			if publishErr != nil {
				logger.Error(clonedCtxWithIncreasedDeadline, "error publishing notification packet with delay", zap.Error(publishErr), zap.String(logger.ORDER_ID, orders[0].GetId()))
			}
		}
	}()

	enableProcessMFOrderETA := s.cfg.Flags().EnableProcessMFOrderETA()
	testPublishEtaDelayEnabled := s.cfg.MFOrderEtaHandlerTestConfig().IsTestEnabled() && lo.Contains(s.cfg.MFOrderEtaHandlerTestConfig().MFAutoIdEnabledActors().ToStringArray(), orders[0].GetActorId())
	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		if enableProcessMFOrderETA || testPublishEtaDelayEnabled {
			logger.Debug(ctx, "publishing delayed message", zap.String(logger.ACTOR_ID_V2, orders[0].GetActorId()), zap.String(logger.ORDER_ID, orders[0].GetId()))
			delayDuration, err := s.getPublishDelayForProcessOrderETA(grpCtx, orders[0])
			if err != nil {
				logger.Error(grpCtx, "error in getPublishDelayForProcessOrderETA", zap.Error(err))
				return err
			}
			_, publishErr := s.orderETADelayPublisher.PublishWithDelay(grpCtx, &etaHandlerPb.ProcessOrderETARequest{
				ETARequestType: &etaHandlerPb.ProcessOrderETARequest_MutualFundOrder{
					MutualFundOrder: orders[0],
				},
			}, delayDuration)
			if publishErr != nil {
				logger.Error(grpCtx, "error publishing ProcessOrderETA packet with delay as ETA", zap.Error(publishErr), zap.String(logger.ORDER_ID, orders[0].GetId()))
				return publishErr
			}
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in publishing for ProcessOrderETA", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, err
	}

	if len(orders) > 1 {
		// This should never happen
		logger.Error(ctx, "multiple orders created for a single buy order", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(createErr),
			zap.String(logger.MF_AMC, fundInfo.Amc.String()))
	}
	return &pb.CreateOrderResponse{
		Status: rpc.StatusOk(),
		Order:  orders[0],
	}, nil
}

func (s *Service) processSellOrderV2(ctx context.Context, req *pb.CreateOrderRequest, fundInfo *mfPb.MutualFund, amcInfo *mfPb.AmcInfo) (*pb.CreateOrderResponse, error) {
	orders, createErr, status := s.sellOrderProcessor.CreateOrder(ctx, req, fundInfo, amcInfo)
	if createErr != nil {
		logger.Error(ctx, "error in CreateOrder", zap.String(logger.ACTOR_ID, req.ActorId),
			zap.String(logger.CLIENT, req.GetClient().String()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientOrderId()), zap.Error(createErr),
			zap.String(logger.MF_AMC, fundInfo.Amc.String()))
		return s.getCreateOrderErrorResponse("error in createOrder for sell", createErr, uint32(status)), nil
	}

	for _, order := range orders {
		// TODO(anand): [Ticket 18012] KIBANA dashboard setup. Remove later when analytics is setup.
		logger.Info(ctx, fmt.Sprintf("mf order created with type %s and subtype %s", order.OrderType.String(),
			order.OrderSubType.String()), zap.String(logger.CLIENT_REQUEST_ID, req.ClientOrderId),
			zap.String(logger.ACTOR_ID, req.ActorId), zap.String(logger.MF_ORDER, order.Id))
		go metrics.RecordOrderCreation(order.OrderStatus)
	}

	enableProcessMFOrderETA := s.cfg.Flags().EnableProcessMFOrderETA()
	testPublishEtaDelayEnabled := s.cfg.MFOrderEtaHandlerTestConfig().IsTestEnabled() && lo.Contains(s.cfg.MFOrderEtaHandlerTestConfig().MFAutoIdEnabledActors().ToStringArray(), orders[0].GetActorId())
	grp, grpCtx := errgroup.WithContext(ctx)

	// nolint:dupl
	grp.Go(func() error {
		if enableProcessMFOrderETA || testPublishEtaDelayEnabled {
			logger.Debug(ctx, "publishing delayed message", zap.String(logger.ACTOR_ID_V2, orders[0].GetActorId()), zap.String(logger.ORDER_ID, orders[0].GetId()))
			delayDuration, err := s.getPublishDelayForProcessOrderETA(grpCtx, orders[0])
			if err != nil {
				logger.Error(grpCtx, "error in getPublishDelayForProcessOrderETA", zap.Error(err))
				return err
			}
			_, publishErr := s.orderETADelayPublisher.PublishWithDelay(grpCtx, &etaHandlerPb.ProcessOrderETARequest{
				ETARequestType: &etaHandlerPb.ProcessOrderETARequest_MutualFundOrder{
					MutualFundOrder: orders[0],
				},
			}, delayDuration)
			if publishErr != nil {
				logger.Error(grpCtx, "error publishing ProcessOrderETA packet with delay as ETA", zap.Error(publishErr), zap.String(logger.ORDER_ID, orders[0].GetId()))
				return publishErr
			}
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in publishing for ProcessOrderETA", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, err
	}
	return &pb.CreateOrderResponse{
		Status: &rpc.Status{Code: uint32(pb.CreateOrderResponse_OK)},
	}, nil
}

func (s *Service) getCreateOrderErrorResponse(shortMessage string, err error, errCode ...uint32) *pb.CreateOrderResponse {
	if len(errCode) > 0 {
		return &pb.CreateOrderResponse{
			Status: &rpc.Status{
				Code:         errCode[0],
				ShortMessage: shortMessage,
				DebugMessage: err.Error(),
			},
		}
	}
	return &pb.CreateOrderResponse{
		Status: &rpc.Status{
			Code:         uint32(pb.CreateOrUpdateFileStateResponse_INTERNAL),
			ShortMessage: shortMessage,
			DebugMessage: err.Error(),
		},
	}
}

// checkOrderEligibility returns true if the order is eligible and false if the order is ineligible
func (s *Service) checkOrderEligibility(ctx context.Context, req *pb.CreateOrderRequest) (bool, *deeplink.Deeplink, pb.IneligibilityReason, error) {
	order := &pb.Order{
		ActorId:   req.GetActorId(),
		Amount:    req.GetAmount(),
		OrderType: req.GetOrderType(),
	}
	res, err := s.CheckOrderEligibility(ctx, &pb.CheckOrderEligibilityRequest{Order: order})
	if te := epifigrpc.RPCError(res, err); te != nil {
		logger.Error(ctx, "error in CheckOrderEligibility rpc", zap.String(logger.ACTOR_ID, order.GetActorId()), zap.Error(te))
		return false, nil, 0, te
	}
	if res.GetEligibilityStatus() == pb.EligibilityStatus_NOT_ELIGIBLE {
		logger.Info(ctx, "actor ineligible to place MF order", zap.String(logger.ACTOR_ID_V2, order.GetActorId()), zap.String("ineligibility reason", res.GetIneligibilityReason().String()))
		return false, res.GetNextStep(), res.GetIneligibilityReason(), nil
	}
	return true, nil, 0, nil
}

// getPublishDelayForProcessOrderETA computes the delay for publishing the event for processing order eta
func (s *Service) getPublishDelayForProcessOrderETA(ctx context.Context, order *pb.Order) (time.Duration, error) {
	// TODO(Mihir): remove after testing in prod
	if s.cfg.MFOrderEtaHandlerTestConfig().IsTestEnabled() && lo.Contains(s.cfg.MFOrderEtaHandlerTestConfig().MFAutoIdEnabledActors().ToStringArray(), order.GetActorId()) {
		return s.cfg.MFOrderEtaHandlerTestConfig().CustomPublishDelay(), nil
	}
	eta, err := s.getEta(ctx, order)
	if err != nil {
		return 0, fmt.Errorf("error in getEta: %w", err)
	}
	return time.Until(eta), nil
}

// getEta computes the order ETA
func (s *Service) getEta(ctx context.Context, mfOrder *pb.Order) (time.Time, error) {
	actorId := mfOrder.GetActorId()
	mf, err := s.mutualFundDao.GetById(ctx, mfOrder.GetMutualFundId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return time.Time{}, err
		}
		logger.Error(ctx, "error in GetById", zap.Error(err), zap.String(logger.ORDER_ID, mfOrder.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, err
	}
	paymentRes, paymentErr := s.paymentHandlerClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
		OrderId:     mfOrder.GetId(),
		PaymentMode: phPb.PaymentMode(mfOrder.GetPaymentMode()),
	})
	if grpcErr := epifigrpc.RPCError(paymentRes, paymentErr); grpcErr != nil {
		if paymentRes.GetStatus().GetCode() == uint32(code.Code_NOT_FOUND) {
			etaDate, etaErr := invPkg.GetETADate(invPkg.ETAParams{
				PendingOrder:  mfOrder,
				AssetClass:    mf.GetAssetClass(),
				PaymentStatus: phPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
				PaymentTime:   nil,
				CategoryName:  mf.GetCategoryName(),
			})
			if err != nil {
				logger.Error(ctx, "Error in GetETADate", zap.Error(etaErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return time.Time{}, err
			}
			return etaDate, nil
		}

		logger.Error(ctx, "Error in GetPaymentDetails", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, grpcErr
	}
	etaDate, err := invPkg.GetETADate(invPkg.ETAParams{
		PendingOrder:  mfOrder,
		AssetClass:    mf.GetAssetClass(),
		PaymentStatus: paymentRes.GetPaymentStatus(),
		PaymentTime:   paymentRes.GetTransactionTime(),
		CategoryName:  mf.GetCategoryName(),
	})
	if err != nil {
		logger.Error(ctx, "Error in GetETADate", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, err
	}
	return etaDate, nil
}
